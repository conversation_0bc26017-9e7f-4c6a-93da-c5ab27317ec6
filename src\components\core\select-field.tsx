import { ArrowDownIcon } from '@/assets/icons';
import { useDisclosure, useQueryString } from '@/hooks';
import { cn } from '@/lib/utils.ts';
import { useEffect, useRef, useState } from 'react';
import EmptyData from './empty-data';
import { OptionsType } from '@/types/common';

type Props = {
  value?: string | number | boolean;
  placeholder?: string;
  error?: string;
  defaultExpanded?: boolean;
  options?: OptionsType[];
  disabled?: boolean;
  selectStyle?: string;
  optionStyle?: string;
  onChange?: (value: string | number | boolean) => void;
};

export const SelectField = ({
  defaultExpanded = false,
  options = [],
  error,
  onChange,
  value,
  disabled,
  placeholder,
  selectStyle,
  optionStyle,
}: Props) => {
  const expandable = useDisclosure(defaultExpanded);
  const selectRef = useRef<HTMLDivElement | null>(null);
  const activeOptionRef = useRef<HTMLDivElement | null>(null);
  const [openUpward, setOpenUpward] = useState(false);
  const { queryString } = useQueryString();
  const valueOption = options?.find((o) => o.value === value)?.label || '';

  useEffect(() => {
    expandable.close();
  }, [queryString.id]);

  useEffect(() => {
    if (expandable.opened && selectRef.current) {
      const rect = selectRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = 500;

      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setOpenUpward(true);
      } else {
        setOpenUpward(false);
      }

      if (activeOptionRef.current) {
        activeOptionRef.current.scrollIntoView({ block: 'center', behavior: 'smooth' });
      }
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        expandable.close();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [expandable.opened, options]);

  return (
    <div className={'flex flex-col gap-2 relative'} ref={selectRef}>
      <div
        className={cn(
          ' rounded-2xl bg-white h-[45px] px-5 flex items-center justify-between cursor-pointer  border border-gray-9 relative',
          expandable.opened && openUpward && 'rounded-t-none',
          expandable.opened && !openUpward && 'rounded-b-none',
          disabled && 'bg-gray-10 cursor-not-allowed',
          error && 'border-red',
          selectStyle
        )}
        onClick={(e) => {
          if (disabled) return;
          e.stopPropagation();
          expandable.toggle();
        }}
      >
        <span className={cn('text-base truncate w-[152px]', !valueOption && 'text-gray-4')}>
          {valueOption || placeholder}
        </span>
        <img
          src={ArrowDownIcon}
          alt={'arrow'}
          className={cn('cursor-pointer', expandable.opened && 'rotate-180')}
        />
      </div>
      {expandable.opened && (
        <div
          className={cn(
            'bg-white border border-gray-9 p-1 flex flex-col gap-1 absolute z-10 w-full max-h-[280px] overflow-y-auto',
            openUpward ? 'bottom-full rounded-t-2xl' : 'top-full rounded-b-2xl'
          )}
        >
          {options.length > 0 ? (
            options.map((option) => (
              <div
                key={String(option.value)}
                ref={option.value === value ? activeOptionRef : null}
                className={cn(
                  'flex items-center justify-center rounded-[10px] h-[35px] font-medium text-sm bg-white hover:bg-gray-6 text-gray-5 select-none cursor-pointer',
                  option.value === value && 'bg-gray-6 hover:bg-gray-6',
                  option.disabled && 'cursor-not-allowed text-gray-8',
                  optionStyle
                )}
                onClick={() => {
                  if (option?.disabled) return;
                  onChange?.(option.value);
                  expandable.close();
                }}
              >
                {option.label}
              </div>
            ))
          ) : (
            <EmptyData className="font-normal text-sm py-1" />
          )}
        </div>
      )}
      {error && <div className="text-red text-sm">{error}</div>}
    </div>
  );
};
