import { IMemePaginationParams } from '../common.types';
import { IMeme } from '../verify-meme/verify-meme.types';

export interface ISwapFee {
  _id: string;
  creatorSolFee: number;
  creatorUsdFee: number;
  airdropSolFee: number;
  airdropUsdFee: number;
  platformSolFee: number;
  platformUsdFee: number;
  volumeUsd: number;
  volume: number;
  tradeCount: number;
  coin: IMeme;
  creatorFeeDeposit: string;
  platformFeeDeposit: string;
  airdropFeeDeposit: string;
}

export interface IStatInfo {
  priceChange: number;
  holderChange: number;
  liquidityChange: number;
  volumeChange: number;
  buyVolume: number;
  sellVolume: number;
  buyOrganicVolume: number;
  sellOrganicVolume: number;
  numBuys: number;
  numSells: number;
  numTraders: number;
  numOrganicBuyers: number;
  numNetBuyers: number;
}

export interface IGraduatedMeme {
  id: string;
  name: string;
  symbol: string;
  icon: string;
  decimals: number;
  twitter: string;
  telegram: string;
  website: string;
  dev: string;
  circSupply: number;
  totalSupply: number;
  tokenProgram: string;
  launchpad: string;
  partnerConfig: string;
  graduatedPool: string;
  graduatedAt: string;
  holderCount: number;
  fdv: number;
  mcap: number;
  usdPrice: number;
  priceBlockId: number;
  liquidity: number;
  stats5m: IStatInfo;
  stats1h: IStatInfo;
  stats6h: IStatInfo;
  stats24h: IStatInfo;
  firstPool: {
    id: string;
    createdAt: string;
  };
  audit: {
    isSus: boolean;
    mintAuthorityDisabled: boolean;
    freezeAuthorityDisabled: boolean;
    topHoldersPercentage: number;
    devBalancePercentage: number;
    devMigrations: number;
  };
  organicScore: number;
  organicScoreLabel: string;
  isVerified: boolean;
  cexes: string[];
  tags: string[];
  updatedAt: string;
}

export enum EFeeTypes {
  CREATOR = 'creatorSolFee',
  PLATFORM = 'platformSolFee',
  AIRDROP = 'airdropSolFee',
}

export enum ESortBy {
  DESC = 'desc',
  ASC = 'asc',
}

export interface IAnalyticParam extends IMemePaginationParams {
  search?: string;
  order_by?: ESortBy;
  start_date?: string;
  end_date?: string;
}
