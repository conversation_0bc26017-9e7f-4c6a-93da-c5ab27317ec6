import { IPagination, IPaginationParams } from '../common.types';

export interface IUserPointParams extends IPaginationParams {
  search: string;
  sortBy?: string;
}

export interface IUserPoint {
  id: number;
  address: string;
  point: number;
  ref_code: string;
  sp_point: number;
  sp_point_updated_at: string | null;
  total_hold_day: number;
  twitter_uid: string | null;
  display_cover: string | null;
  display_icon: string | null;
  display_name: string | null;
  nonce: string | null;
  created_at: string;
  updated_at: string;
  inviter_code: string | null;
  uuid: string;
  is_limit_invite: boolean;
  solana_address: string | null;
  user_ranking: {
    id: number;
    user_id: number;
    point: number;
    today_point: number;
    today_point_updated_at: string;
    swap_point: number;
    old_point: number;
    rank: string;
    yesterday_rank: number;
    created_at: string;
    updated_at: string;
  };
  point_history: IPointHistory[];
  totalVolume: number;
}
export interface IPointHistory {
  id: number;
  title: string;
  point: number;
  usd_transfer: number;
  user_id: number;
  type: number;
  display_icon: string;
  hash: string;
  created_at: string;
  updated_at: string;
}
export interface IUserPointResponse {
  data: IUserPoint[];
  pagination: IPagination;
}

export interface IUserPointFormPayload {
  id?: number;
  point?: number;
  address: string;
  is_limit_invite?: boolean;
}
