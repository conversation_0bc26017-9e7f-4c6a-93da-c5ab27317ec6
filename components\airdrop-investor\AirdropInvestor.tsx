'use client';
import AirdropThumb from '@/public/images/airdrop-thumb.png';
import AirdropBg from '@/public/images/airdrop-bg.png';
import Image from 'next/image';
import clsx from 'clsx';
// import NumberDisplay from '../shared/NumberDisplay';
import { IoIosArrowForward } from 'react-icons/io';
import Smapocke from '@/public/icons/smapocke_v2.png';
import { useAuth } from '@/hooks/use-auth';
import { useGetUserRank } from '@/services/auth';
import { useGetSummaryPoint } from '@/services/airdrop';
import {
  formatNumber,
  formatNumberWithCommas,
  formatNumberWithSubscriptZeros,
} from '@/utils/common';
import { floor, round } from 'lodash';
import { PublicKey } from '@solana/web3.js';
import Link from 'next/link';
import { routePaths } from '@/constants/common';
import useTokenAccountBuyOwnerBalance from '@/hooks/use-token-account-by-owner-balance';
import { useGetTotalAirdropSpInfo } from '@/services/airdropSp/airdrop.queries';
import { useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import Button, { BUTTON_SIZE } from '../shared/Button';

export default function AirdropInvestor() {
  const { isAuthenticated, user } = useAuth();
  const { data: userRanking } = useGetUserRank(isAuthenticated);
  const { data: airdropSpInfo, isLoading: loadingTotalAirdrop } = useGetTotalAirdropSpInfo();
  const totalAirdropPercent = useMemo(() => {
    return (
      (airdropSpInfo?.pkm.sp_percent || 0) * 100 +
      (airdropSpInfo?.holy.sp_percent || 0) * 100 +
      (airdropSpInfo?.ranking.sp_percent || 0) * 100
    );
  }, [airdropSpInfo]);
  return (
    <>
      <div
        className="w-full h-60 flex flex-col-reverse absolute top-0 sm:rounded-std overflow-hidden"
        style={{
          backgroundImage: `url(${AirdropBg.src})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
          zIndex: '1',
        }}
      >
        <div
          className="h-36"
          style={{
            background:
              'linear-gradient(180deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.6) 48.76%, #F5F5F5 96.56%)',
          }}
        ></div>
      </div>
      <div className="flex flex-col relative z-10 pt-10 px-[18px] grow">
        <div className="flex flex-col gap-5 items-center grow">
          <Image
            src={AirdropThumb.src}
            alt="Airdrop Thumbnail"
            width={160}
            height={0}
            layout="intrinsic"
          />
          <div className="flex flex-col text-center">
            <span className="text-gray-800 text-xl font-medium">Be ready for $SP AirDrop!!</span>
            <span className="text-gray-600 text-base ">October 2025</span>
          </div>
          <div className="w-full flex flex-col gap-2.5 grow">
            <div className="flex flex-col bg-white rounded-std pt-5 pb-2.5 px-[18px] gap-2.5 grow">
              <div className="flex flex-col gap-3.5">
                <div className="w-full flex flex-col gap-1 bg-gray-300 py-4 px-5 rounded-2xl">
                  <span className="text-gray-600 text-sm font-medium">Total $SP Airdrop</span>
                  <div className="w-full flex justify-between">
                    <span
                      className={clsx(
                        'text-gray-800 text-xl font-medium',
                        loadingTotalAirdrop && 'animate-pulse'
                      )}
                    >
                      {formatNumberWithCommas(floor(airdropSpInfo?.totalSp || 0))}
                      <span className="text-sm"> SP</span>
                    </span>
                    <span className="rounded-full py-1.5 px-4 bg-white text-gray-800 text-xs font-medium">
                      Today :&nbsp;
                      {/* // TODO: add loading state */}
                      <span className={clsx('text-green-600', false && 'animate-pulse')}>
                        {/* TODO: fix data display */}
                        +0SP
                        {/* <NumberDisplay number={'1234'} /> */}
                      </span>
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center text-primary-500 bg-primary-500/10 rounded-2xl px-5 py-2">
                  <span className="text-sm font-medium">Your Airdrop Allocation</span>
                  <div className="text-xl font-medium">
                    <span className="text-sm">
                      {formatNumberWithSubscriptZeros(totalAirdropPercent.toString())}%
                    </span>
                    <span className="text-xs"> / 100%</span>
                  </div>
                </div>
              </div>
              <div className="flex gap-[18px] py-3.5">
                <Image
                  src={Smapocke.src}
                  alt={'Airdrop Logo'}
                  width={48}
                  height={48}
                  className="rounded-full size-12 object-cover"
                />
                <div className="w-full flex items-center justify-between">
                  <div className="flex flex-col gap-2">
                    <div className="font-medium text-gray-900 leading-none ">$SP</div>
                    {/* <span className="text-sm text-gray-600 leading-none ">
                      Available : 56,789 SP
                    </span> */}
                  </div>
                  <Button size={BUTTON_SIZE.SMALL} className="!px-4" disabled>
                    Claim
                  </Button>
                </div>
              </div>
            </div>
            <span className="text-center text-xs text-gray-800">
              Daily AirDrop : 0.0033% to claim
            </span>
          </div>
        </div>
      </div>
    </>
  );
}
