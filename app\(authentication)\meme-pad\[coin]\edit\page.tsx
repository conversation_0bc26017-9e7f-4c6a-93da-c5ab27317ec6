'use client';
import React, { useEffect, useRef, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Image from 'next/image';
import Button, { BUTTON_VARIANT } from '@/components/shared/Button';
import { toast } from 'react-toastify';

// Icons
import DatabaseIcon from '@/public/icons/database.svg';
import TrashIcon from '@/public/icons/trash-icon.svg';
import AddPhotoIcon from '@/public/icons/Icon.svg';
import HeaderTitle from '@/features/layout/HeaderTitle';
import {
  getFlexibleSocialUrlErrorMessage,
  getSocialPlatformPrefix,
  isValidSocialUrl,
} from '@/utils/common';
import { useGetMemeDetail, useUpdateMeme } from '@/services/meme-launchpad';
import { routePaths, SOCIAL_LINKS } from '@/constants/common';
import { EMemePadTabs } from '@/constants/meme-pad';
import { FILE_TYPE_ARR, MAX_IMAGES } from '@/constants/memePad';
import { AuthService } from '@/services/auth';
import axios from 'axios';
import LoadingSpin from '@/components/shared/LoadingSpin';
import { PlusIcon } from '@radix-ui/react-icons';

interface EditTokenFormData {
  description: string | undefined;
  descriptionImages: string[] | undefined;
  socials: { [key: string]: string };
}

export default function EditTokenPage() {
  const router = useRouter();
  const { coin: tokenAddress } = useParams() as { coin: string };
  const { data: coinDetail, isLoading } = useGetMemeDetail(tokenAddress);
  const { mutate: updateMeme } = useUpdateMeme(tokenAddress);

  const [isOpenSocialLink, setIsOpenSocialLink] = useState(false);
  const [selectedSocials, setSelectedSocials] = useState<string[]>([]);
  const [isUploadingDescriptionImage, setIsUploadingDescriptionImage] = useState<boolean>(false);

  const descriptionFileInputRef = useRef<HTMLInputElement | null>(null);
  const {
    register,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors, isSubmitting },
    watch,
    control
  } = useForm<EditTokenFormData>({
    defaultValues: {
      description: coinDetail?.description,
      descriptionImages: coinDetail?.descriptionImages,
      socials: coinDetail?.socials
        .map((social) => {
          return { [social.name]: social.url };
        })
        .reduce((acc, cur) => ({ ...acc, ...cur }), {}),
    },
    mode: 'onChange',
  });

  const { fields, append, remove } = useFieldArray({
    name: 'socials',
    control,
  });

  useEffect(() => {
    if (coinDetail?.socials?.length) {
      const newSelectedSocials: string[] = [];

      coinDetail.socials.map((social) => {
        setValue('socials', { ...getValues('socials'), [social.name]: social.url });
        newSelectedSocials.push(social.name);
      });

      setSelectedSocials(newSelectedSocials);
    }
    if (coinDetail?.descriptionImages) {
      setValue('descriptionImages', coinDetail?.descriptionImages);
    }
    if (coinDetail?.description) {
      setValue('description', coinDetail?.description);
    }
  }, [coinDetail, setValue]);

  const toggleSocialSelection = (key: string) => {
    if (selectedSocials.includes(key)) {
      setValue('socials', { ...getValues('socials'), [key]: '' });
      setSelectedSocials((prev) => prev.filter((item) => item !== key));
    } else {
      setSelectedSocials((prev) => [...prev, key]);
    }
  };

  // Handle form submission
  const onSubmit = async (data: EditTokenFormData) => {
    try {
      // Create social links array from form data
      const socialLinks = selectedSocials
        .map((name) => {
          const url = data.socials[name]?.trim();
          return url ? { name, url } : null;
        })
        .filter(Boolean) as { name: string; url: string }[];

      updateMeme({
        socials: socialLinks,
        description: data.description || '',
        descriptionImages: data.descriptionImages,
      });
      toast.success('Token updated successfully');
      router.push(`${routePaths.memePad}?tab=${EMemePadTabs.MY_TOKENS}`);
    } catch (error) {
      toast.error('Failed to update token');
    }
  };

  const removeDescriptionImage = (index: number) => {
    setValue(
      'descriptionImages',
      watch('descriptionImages')?.filter((_, indexImg) => indexImg !== index)
    );
  };

  const triggerDescriptionFileInput = () => {
    if (descriptionFileInputRef.current) {
      descriptionFileInputRef.current.click();
    }
  };

  const handleDescriptionImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsUploadingDescriptionImage(true);
    const desImages = watch('descriptionImages') || [];
    if (!event.target.files || event.target.files.length === 0) {
      return;
    }

    try {
      const files = Array.from(event.target.files);
      const remainingSlots = MAX_IMAGES - desImages.length;

      if (files.length > remainingSlots) {
        toast.warning(`You can only upload ${remainingSlots} more image(s)`);
        files.splice(remainingSlots);
      }

      // Check file types and sizes
      for (const file of files) {
        if (!file.type.startsWith('image/') || !FILE_TYPE_ARR.includes(file.type)) {
          toast.error(`${file.name} is not an image file`);
          return;
        }
        if (file.size > 10 * 1024 * 1024) {
          toast.error(`${file.name} exceeds 10MB limit`);
          return;
        }
      }

      // Generate unique filenames
      const fileNames = files.map(
        (file) => crypto.randomUUID() + file.name.substring(file.name.lastIndexOf('.'))
      );

      // Get presigned URLs from server
      const response = await AuthService.generatePresignedUrl({
        fileNames: fileNames,
      });

      // Upload files and collect URLs
      const uploadedUrls: string[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const presignedUrl = response[i];

        await axios.put(presignedUrl.presignedUrl, file, {
          headers: {
            'Content-Type': file.type,
          },
        });

        const imageUrl = presignedUrl.presignedUrl
          .split('?')[0]
          .replace(
            process.env.NEXT_PUBLIC_BUCKET_DOMAIN!,
            process.env.NEXT_PUBLIC_CLOUDFRONT_DOMAIN!
          );

        uploadedUrls.push(imageUrl);
      }

      const updatedImages = [...desImages, ...uploadedUrls];
      setValue('descriptionImages', updatedImages, { shouldValidate: true });

      toast.success(`${files.length} image(s) uploaded successfully`);
    } catch {
      toast.error('Failed to upload images');
    } finally {
      setIsUploadingDescriptionImage(false);
    }

    // Reset the input value to allow uploading the same files again
    event.target.value = '';
  };

  if (isLoading) return <div className="flex justify-center p-8">Loading...</div>;

  return (
    <div className="flex flex-col items-center justify-center pb-8">
      <div className="w-full max-w-3xl">
        <HeaderTitle title="Edit Token" />

        <div className="mt-6">
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col bg-white rounded-3xl p-6"
          >
            {/* Header */}
            <div className="flex items-center mb-6">
              <div className="w-12 h-12 rounded-full bg-[#EE811A1A] flex items-center justify-center">
                <DatabaseIcon size={18} />
              </div>
              <h2 className="text-xl font-medium text-[#666666] ml-3">Edit Token</h2>
            </div>

            {/* Token Info */}
            <div className="flex items-start mb-6">
              <div className="rounded-full w-12 h-12">
                <Image
                  src={coinDetail?.iconUri || '/images/smart-pocket-default-image.png'}
                  alt={coinDetail?.symbol || 'Token'}
                  width={48}
                  height={48}
                  className="rounded-full w-12 h-12"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/images/smart-pocket-default-image.png';
                  }}
                />
              </div>

              {/* Token Info */}
              <div className="flex-1 h-12">
                <div className="flex justify-between items-start w-full pl-4 h-full">
                  <div className="flex flex-col justify-between items-start h-full">
                    <h1 className="text-base font-medium text-[#4C4C4C]">{coinDetail?.symbol}</h1>
                    <p className="text-sm font-medium text-[#999999]">{coinDetail?.name}</p>
                  </div>
                </div>
              </div>
            </div>
            {/* Description & Images */}
            <div className="mb-3">
              <div className="flex justify-between items-center mb-2">
                <label htmlFor="description" className="text-[#666666] text-base font-normal">
                  Description
                </label>
                <span className="text-[#666666] text-xs font-normal">100</span>
              </div>
              <div className="flex flex-col std:flex-row gap-3">
                <div className="w-full">
                  <textarea
                    id="description"
                    {...register('description', {
                      maxLength: {
                        value: 100,
                        message: 'Description cannot exceed 100 characters',
                      },
                    })}
                    maxLength={100}
                    placeholder="Summary of token description"
                    className={`w-full border ${errors.description ? 'border-red-500' : 'border-gray-200'} rounded-xl p-3 bg-gray-300 text-[#999999] placeholder:text-[#999999] focus:outline-none h-24 std:h-full resize-none`}
                    onBlur={(e) => {
                      e.target.value = e.target.value.trim();
                    }}
                  />
                  {errors.description && (
                    <p className="text-xs text-red-500 mt-1">{errors.description.message}</p>
                  )}
                </div>
                <div className="flex flex-col gap-[10px] mb-6 std:mb-0 w-full">
                  <div className="flex flex-wrap gap-[10px]">
                    {getValues('descriptionImages')?.map((imageUrl, index) => (
                      <div key={'desc-img-' + index} className="relative group">
                        <Image
                          src={imageUrl}
                          alt={`Description image ${index + 1}`}
                          width={160}
                          height={160}
                          className="rounded-[12px] w-[72px] h-[72px] std:w-[82px] std:h-[82px] object-cover"
                        />
                        <button
                          type="button"
                          onClick={() => removeDescriptionImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          ×
                        </button>
                      </div>
                    ))}

                    {(watch('descriptionImages')?.length || 0) < MAX_IMAGES && (
                      <div
                        onClick={triggerDescriptionFileInput}
                        className="rounded-[12px] w-[72px] h-[72px] std:w-[82px] std:h-[82px] bg-gray-300 border border-[#F5F5F5] flex items-center justify-center cursor-pointer hover:bg-[#E5E5E5] transition-colors"
                      >
                        {isUploadingDescriptionImage ? <LoadingSpin /> : <PlusIcon />}
                        <input
                          ref={descriptionFileInputRef}
                          type="file"
                          accept="image/jpeg, image/png, image/gif, image/webp"
                          multiple
                          className="hidden"
                          onChange={handleDescriptionImageUpload}
                          aria-label="Upload description images"
                          disabled={isUploadingDescriptionImage}
                        />
                      </div>
                    )}
                  </div>
                  <div className="text-xs text-[#666666]">
                    <p className="leading-[24px]">Upload image or gif up to 10 MB, 400 × 400</p>
                    <p className="leading-[24px]">Maximum of 4 images</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Input fields for selected social platforms */}
            {selectedSocials.length > 0 && (
              <div className="space-y-4 mb-6">
                {selectedSocials.map((socialKey) => {
                  const socialItem = SOCIAL_LINKS.find((item) => item.key === socialKey);
                  if (!socialItem) return null;

                  return (
                    <div key={socialKey} className="mb-4">
                      <div className="flex justify-between items-center mb-[10px]">
                        <div className="flex items-center">
                          {React.createElement(socialItem.icon)}
                          <label className="text-[#666666] text-base font-normal ml-2">
                            {socialItem.name}
                          </label>
                        </div>
                        <span className="text-[#666666] text-xs font-normal">Optional</span>
                      </div>

                      <div className="relative">
                        <input
                          {...register(`socials.${socialKey}`, {
                            validate: (value) => {
                              if (typeof value !== 'string') return true;
                              if (value && value.trim().length === 0)
                                return 'Field cannot be only whitespace';
                              if (!value) return true;

                              return (
                                isValidSocialUrl(value.trim(), socialKey) ||
                                getFlexibleSocialUrlErrorMessage(value.trim(), socialKey)
                              );
                            },
                          })}
                          placeholder={`${getSocialPlatformPrefix(socialKey)}your-link`}
                          className={`w-full border pr-20 ${errors.socials?.[socialKey] ? 'border-red-500' : 'border-gray-200'
                            } rounded-xl p-[16px] bg-gray-300 text-[#999999] placeholder:text-[#999999] focus:outline-none`}
                          onBlur={(e) => {
                            e.target.value = e.target.value.trim();
                          }}
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center justify-center h-8 w-11 bg-[#FFFFFF] rounded-2xl"
                          onClick={() => toggleSocialSelection(socialKey)}
                        >
                          <TrashIcon />
                        </button>
                      </div>
                      {errors.socials?.[socialKey] && (
                        <p className="text-red-500 text-xs mt-1">
                          {errors.socials?.[socialKey]?.message?.toString()}
                        </p>
                      )}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Social Platform Links */}
            <div>
              <div
                className={`flex ${isOpenSocialLink ? 'items-start' : 'items-center'} justify-between gap-[10px] mb-[50px] w-full`}
              >
                <div
                  onClick={() => setIsOpenSocialLink((prev) => !prev)}
                  className="min-w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center cursor-pointer"
                >
                  <AddPhotoIcon size={18} />
                </div>
                {isOpenSocialLink ? (
                  <div className="flex flex-wrap gap-2">
                    {SOCIAL_LINKS.map((link) => (
                      <div
                        key={link.key}
                        onClick={() => toggleSocialSelection(link.key)}
                        className={`flex items-center justify-center gap-[10px] ${selectedSocials.includes(link.key)
                          ? 'bg-[#EE811A] text-white'
                          : 'bg-[#F0EEEE] text-[#666666]'
                          } rounded-[14px] py-[8px] px-[14px] text-sm font-medium cursor-pointer`}
                      >
                        {React.createElement(link.icon)}
                        <span className="text-xs">{link.name}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex justify-between items-center w-full">
                    <div className="text-[#666666] text-base font-normal ml-4">Social</div>
                    <div className="text-[#666666] text-xs font-normal ml-auto">
                      {selectedSocials.length > 0 ? `${selectedSocials.length} selected` : ''}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="flex gap-4">
              <Button
                type="submit"
                variant={BUTTON_VARIANT.PRIMARY}
                className="flex-1 !bg-[#EE811A] !py-4 !rounded-xl text-white font-medium"
              >
                {isSubmitting ? 'Updating...' : 'Done'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
