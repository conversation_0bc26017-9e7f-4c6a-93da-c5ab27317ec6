import { LoadingScreen } from '@/components/core';
import EmptyData from '@/components/core/empty-data';
import <PERSON><PERSON>iewer from '@/components/core/image-viewer';
import { SearchInputField } from '@/components/core/search-input-field';
import { SelectField } from '@/components/core/select-field';
import { Button } from '@/components/ui';
import { EPaginationDefault } from '@/constants/common';
import { useQueryString } from '@/hooks';
import useScroll from '@/hooks/use-scroll';
import { useSearch } from '@/hooks/use-search';
import { copyTextToClipboard, formatAddress } from '@/lib/utils';
import { useGetInfiniteSwapFee } from '@/services/swap-fee/swap-fee.query';
import { EFeeTypes, ESortBy } from '@/services/swap-fee/swap-fee.type';
import BigNumber from 'bignumber.js';
import clsx from 'clsx';
import { get, values } from 'lodash';
import { useMemo, useState } from 'react';

const feeTabs = [
  {
    label: 'Creator',
    value: EFeeTypes.CREATOR,
  },
  {
    label: 'Platform',
    value: EFeeTypes.PLATFORM,
  },
  {
    label: 'Airdrop Rank',
    value: EFeeTypes.AIRDROP,
  },
];

const getDepositWalletAddress = (type: EFeeTypes) => {
  switch (type) {
    case EFeeTypes.CREATOR:
      return 'creatorFeeDeposit';
    case EFeeTypes.PLATFORM:
      return 'platformFeeDeposit';
    case EFeeTypes.AIRDROP:
      return 'airdropFeeDeposit';
  }
};

enum EDurationType {
  ONE_DAY = '24H',
  ALL_TIME = 'ALL_TIME',
}

export default function BeforeGraduateTokenFee() {
  const { handleSearch } = useSearch('search');
  const { queryString } = useQueryString();

  const [currentFeeType, setCurrentFeeType] = useState<EFeeTypes>(EFeeTypes.CREATOR);
  const [now] = useState<Date>(new Date());
  const [currentDurationType, setCurrentDurationType] = useState<EDurationType>(
    EDurationType.ONE_DAY
  );
  const [sortBy, setSortBy] = useState<ESortBy>(ESortBy.DESC);

  const getDurationParam = (type: EDurationType) => {
    switch (type) {
      case EDurationType.ONE_DAY:
        return {};
      case EDurationType.ALL_TIME:
        return {
          start_date: new Date(0).toISOString(),
          end_date: now.toISOString(),
        };
    }
  };

  const { data, isLoading, hasNextPage, fetchNextPage, isFetchingNextPage } = useGetInfiniteSwapFee(
    {
      search: queryString.search || '',
      order_by: sortBy,
      ...getDurationParam(currentDurationType),
      page: EPaginationDefault.PAGE,
      limit: EPaginationDefault.PER_PAGE,
    }
  );

  const flattenData = useMemo(() => {
    return data ? data.pages.flatMap((page) => page.docs) : [];
  }, [data]);

  const headers = useMemo(() => {
    return [
      {
        label: 'Token',
        value: 'coin',
      },
      {
        label: 'Volume',
        value: 'volume',
        currency: 'SOL',
      },
      {
        label: 'Profit',
        value: currentFeeType,
        currency: 'SOL',
      },
      {
        label: 'Deposit Wallet Address',
        value: getDepositWalletAddress(currentFeeType),
        copyable: true,
        isAddress: true,
      },
    ];
  }, [currentFeeType]);

  const { handleScroll } = useScroll(fetchNextPage, hasNextPage, isFetchingNextPage);

  const renderList = () => {
    if (isLoading) {
      return <LoadingScreen />;
    }
    if (data && flattenData.length > 0) {
      return (
        <>
          {flattenData?.map((item) => {
            return (
              <tr key={item._id} className="w-full table table-fixed border-b border-gray-3">
                {headers.map((header) => {
                  if (header.value === 'coin') {
                    const { iconUri, name, symbol, tokenAddress } = get(item, header.value, {
                      iconUri: '',
                      name: '???',
                      symbol: '???',
                      tokenAddress: '???',
                    });
                    return (
                      <td
                        key={header.value}
                        className="py-[22px] px-2 min-w-fit text-sm text-gray-5 font-medium flex items-center gap-2"
                      >
                        <ImageViewer
                          url={iconUri}
                          className="min-w-12 w-12 h-12 rounded-full overflow-hidden"
                          imageClassName="object-cover"
                        />
                        <div className="flex flex-col gap-1">
                          <span>
                            {name}&nbsp;(${symbol})
                          </span>
                          <span
                            className="text-xs text-gray-4 cursor-pointer"
                            onClick={() => copyTextToClipboard(tokenAddress)}
                          >
                            {formatAddress(tokenAddress)}
                          </span>
                        </div>
                      </td>
                    );
                  }
                  return (
                    <td
                      key={header.value}
                      className={clsx(
                        'py-[22px] px-2 min-w-fit text-sm text-gray-5 font-medium',
                        header.copyable && 'cursor-pointer'
                      )}
                      onClick={() =>
                        header.copyable && copyTextToClipboard(get(item, header.value, '-'))
                      }
                    >
                      {header.isAddress
                        ? formatAddress(get(item, header.value, '-'))
                        : new BigNumber(get(item, header.value, '-'))
                            .toFixed(9)
                            .replace(/(\.\d*?[1-9])0+$/, '$1')
                            .replace(/\.0+$/, '')}
                      &nbsp;{header.currency || ''}
                    </td>
                  );
                })}
              </tr>
            );
          })}
        </>
      );
    }
    return <EmptyData />;
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col items-end gap-3 w-full">
        <SearchInputField
          name="search"
          placeholder="Token address, symbol, name"
          onChange={handleSearch}
          defaultValue={queryString.search || ''}
          className="w-96"
        />
        <div className="flex justify-between w-full">
          <div className="flex gap-2 items-center">
            {feeTabs.map((tab) => {
              const isActive = tab.value === currentFeeType;
              return (
                <Button
                  key={tab.value}
                  variant={isActive ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setCurrentFeeType(tab.value)}
                >
                  {tab.label}
                </Button>
              );
            })}
          </div>
          <div className="flex gap-4">
            <div className="flex items-center gap-2 text-gray-5">
              Period&nbsp;
              <SelectField
                selectStyle="w-[200px]"
                placeholder="All"
                options={values(EDurationType).map((value) => ({
                  label: value ? value.toLocaleLowerCase().replace('_', ' ') : 'All',
                  value: value,
                }))}
                onChange={(val) => {
                  setCurrentDurationType(val as EDurationType);
                }}
                value={currentDurationType}
              />
            </div>
            <div className="flex items-center gap-2 text-gray-5">
              Sort by&nbsp;
              <SelectField
                selectStyle="w-[200px]"
                placeholder="Transaction date DESC"
                options={values(ESortBy).map((value) => ({
                  label: 'Volume/Profit ' + value.toLocaleUpperCase(),
                  value: value,
                }))}
                onChange={(val) => {
                  setSortBy(val as ESortBy);
                }}
                value={sortBy || undefined}
              />
            </div>
          </div>
        </div>
      </div>
      <table className="w-full table-auto">
        <thead>
          <tr className="w-full table table-fixed border-b border-gray-3 text-left text-gray-5  whitespace-nowrap">
            {headers.map((header) => (
              <th key={header.label} className="pb-2.5 px-2 font-semibold">
                <div className="flex items-center gap-1">{header.label}</div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="block h-[calc(100dvh-421px)] overflow-y-auto" onScroll={handleScroll}>
          {renderList()}
        </tbody>
      </table>
    </div>
  );
}
