import { ArrowRightIcon } from '@/assets/icons';
import { LoadingScreen } from '@/components/core';
import { Badge } from '@/components/core/badge';
import EmptyData from '@/components/core/empty-data';
import PageTitle from '@/components/core/page-title';
import { SearchInputField } from '@/components/core/search-input-field';
import { SelectField } from '@/components/core/select-field';
import { OPEN_FORM } from '@/constants';
import { EPaginationDefault } from '@/constants/common';
import { useQueryString } from '@/hooks';
import useScroll from '@/hooks/use-scroll';
import { useSearch } from '@/hooks/use-search';
import { cn, formatAddress } from '@/lib/utils';
import { IUserPoint } from '@/services/user-point';
import { useGetUserPoint } from '@/services/user-point/user-point.queries';
import { floor } from 'lodash';
import { useMemo } from 'react';

const TransactionsUserList = () => {
  const { handleSearch } = useSearch('search');
  const { queryString, setQueryString } = useQueryString();
  const { data, isLoading, hasNextPage, fetchNextPage, isFetchingNextPage } = useGetUserPoint({
    search: queryString.search || '',
    sortBy: queryString.sortBy || 'rank',
    page: EPaginationDefault.PAGE,
    perPage: EPaginationDefault.PER_PAGE,
  });
  const { handleScroll } = useScroll(fetchNextPage, hasNextPage, isFetchingNextPage);

  const userList = useMemo(() => {
    return data?.pages.flatMap((page) => page.data);
  }, [data]);

  const toggleItem = (item_address: string) => {
    const address = item_address === queryString.address ? '' : item_address;
    setQueryString({
      address,
      [OPEN_FORM]: !address && queryString[OPEN_FORM] ? 'false' : 'true',
    });
  };

  const renderList = () => {
    if (isLoading) {
      return <LoadingScreen />;
    }
    if (userList && userList.length > 0) {
      return (
        <>
          <div className="flex justify-between p-4">
            <div className="text-base text-gray-7 font-medium">User Solana Address</div>
            <div className="flex items-center">
              <SelectField
                options={[
                  { label: 'Total Points', value: 'rank' },
                  { label: 'Total Volume', value: 'totalVolume' },
                ]}
                selectStyle="h-[40px] items-center text-gray-5"
                placeholder="Total Points"
                value={queryString.sortBy || 'rank'}
                onChange={(v) => setQueryString({ sortBy: String(v) })}
              ></SelectField>
            </div>
          </div>
          {userList.map((item: IUserPoint) => {
            const isActive = item.address === queryString.address;
            const address = item.solana_address && formatAddress(item.solana_address || '');
            return (
              <div
                key={item.id}
                onClick={() => toggleItem(String(item.solana_address))}
                className={cn(
                  'flex justify-between items-center cursor-pointer border-t border-gray-9  font-medium text-sm pt-3 px-4 py-[10px] h-[60px]',
                  isActive ? 'text-orange-1' : 'text-gray-5'
                )}
              >
                <div className="truncate  whitespace-nowrap overflow-hidden flex items-center gap-2">
                  <Badge
                    color="bg-orange-1"
                    className="font-semibold"
                    label={item.user_ranking.rank}
                  />
                  {address}
                </div>
                <div className="flex gap-2">
                  <div className="flex flex-col text-right">
                    <span className="flex gap-1 justify-end font-normal">
                      <span className="font-medium">Total Points:</span>
                      {floor(item.user_ranking.point)} PT
                    </span>
                    <span className="flex gap-1 justify-end font-normal">
                      <span className="font-medium">Total Volume:</span>
                      {item.totalVolume}
                    </span>
                  </div>
                  <img src={ArrowRightIcon} alt="" />
                </div>
              </div>
            );
          })}
        </>
      );
    }
    return <EmptyData />;
  };

  return (
    <>
      <div>
        <PageTitle title="User Point" />
        <SearchInputField
          name="search"
          placeholder="0x...0000"
          onChange={handleSearch}
          defaultValue={queryString.search || ''}
        />
      </div>
      <div
        className="mt-6 overflow-y-auto h-[calc(100vh-203px)] border border-gray-9 bg-white rounded-[28px]"
        onScroll={handleScroll}
      >
        <div className="flex flex-col p-7">{renderList()}</div>
      </div>
    </>
  );
};

export default TransactionsUserList;
