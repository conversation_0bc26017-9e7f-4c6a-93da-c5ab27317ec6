import { ESortBy, ESortOrder, IListResponse } from '../verify-meme/verify-meme.types';

export enum TRADETYPE {
  BUY = 'BUY',
  SELL = 'SELL',
}

export interface ITransactionsParams {
  solanaAddress: string;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: ESortBy;
  sortOrder?: ESortOrder;
}

export interface ITransaction {
  timestamp: number;
  volume: string;
  solScanLink: string;
  tokenSymbol: string;
  hash: string;
  tradeType: string;
}

export interface IListTransactions extends IListResponse<ITransaction> {
  totalVolume: number;
}
