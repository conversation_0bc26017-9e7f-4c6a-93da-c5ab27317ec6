import { LoadingScreen } from '@/components/core';
import EmptyData from '@/components/core/empty-data';
import PageTitle from '@/components/core/page-title';
import { Button } from '@/components/ui';
import { EPaginationDefault } from '@/constants/common';
import useScroll from '@/hooks/use-scroll';
import { cn } from '@/lib/utils';
import { useGetTransactions } from '@/services/transactions/transactions.query';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

const Transactions = () => {
  const [searchParams] = useSearchParams();
  const address = searchParams.get('address');
  const { data, isLoading, hasNextPage, fetchNextPage, isFetchingNextPage } = useGetTransactions({
    solanaAddress: address || '',
    page: EPaginationDefault.PAGE,
    limit: EPaginationDefault.PER_PAGE,
  });
  const transactions = useMemo(() => {
    return data?.pages.flatMap((page) => page.docs);
  }, [data]);
  const totalVolume = useMemo(() => {
    return data?.pages.flatMap((page) => page.totalVolume);
  }, [data]);
  const { handleScroll } = useScroll(fetchNextPage, hasNextPage, isFetchingNextPage);

  const renderList = () => {
    if (isLoading) {
      return <LoadingScreen />;
    }
    if (transactions && transactions.length > 0) {
      return (
        <>
          {transactions.map((trans, index) => {
            const date = new Date(trans.timestamp * 1000); // Convert UNIX timestamp to Date
            return (
              <div className="flex gap-2 text-gray-5 text-sm" key={index}>
                <div className="flex flex-col grow">
                  <div className="w-full flex justify-between">
                    <div className="flex gap-1">
                      <span>{trans.tokenSymbol}</span>
                    </div>
                    <span className={cn(trans.tradeType === 'BUY' ? 'text-green-1' : 'text-red-2')}>
                      {trans.volume}
                    </span>
                  </div>
                  <span>{`${dayjs(date).format('YYYY-MM-DD HH:mm:ss')}`}</span>
                </div>
                <a
                  href={
                    trans.hash
                      ? `https://solscan.io/tx/${trans.hash}${import.meta.env.VITE_SOLANA_NETWORK !== 'mainnet' ? '?cluster=devnet' : ''}`
                      : '#'
                  }
                  target="_blank"
                  rel="noreferrer"
                >
                  <Button size={'sm'} variant={'primary'} className="bg-green-1">
                    Check SolScan
                  </Button>
                </a>
              </div>
            );
          })}
        </>
      );
    }
    return <EmptyData />;
  };

  return (
    <>
      <PageTitle title="Transactions" />
      <div
        className="flex flex-col gap-6 w-full h-[calc(100vh-142px)] overflow-y-auto border border-gray-9 bg-white rounded-[28px] p-7"
        onScroll={handleScroll}
      >
        <div className="text-base font-medium">Total Volume: {totalVolume}</div>
        {renderList()}
      </div>
    </>
  );
};

export default Transactions;
